-- Database setup untuk aplikasi pencatatan kesehatan lansia
-- Pastikan MySQL server sudah ber<PERSON>lan dengan user: root, password: root

-- Buat database jika belum ada
CREATE DATABASE IF NOT EXISTS lansia CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- Gunakan database lansia
USE lansia;

-- Tabel untuk menyimpan profil lansia
CREATE TABLE IF NOT EXISTS profiles (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nama VARCHAR(100) NOT NULL,
    usia INT NOT NULL,
    alamat TEXT NOT NULL,
    riwayat_medis TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Tabel untuk menyimpan riwayat pemeriksaan kesehatan
CREATE TABLE IF NOT EXISTS checkups (
    id INT AUTO_INCREMENT PRIMARY KEY,
    profile_id INT NOT NULL,
    tekanan_darah VARCHAR(20) NOT NULL,
    gula_darah INT NOT NULL,
    tanggal DATETIME DEFAULT CURRENT_TIMESTAMP,
    catatan TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (profile_id) REFERENCES profiles(id) ON DELETE CASCADE
);

-- Index untuk optimasi query
CREATE INDEX idx_profile_id ON checkups(profile_id);
CREATE INDEX idx_tanggal ON checkups(tanggal);

-- Insert data contoh untuk testing
INSERT INTO profiles (nama, usia, alamat, riwayat_medis) VALUES 
('Budi Santoso', 65, 'Jl. Merdeka No. 123, Jakarta', 'Hipertensi, Diabetes'),
('Siti Aminah', 70, 'Jl. Sudirman No. 456, Bandung', 'Kolesterol tinggi'),
('Ahmad Rahman', 68, 'Jl. Gatot Subroto No. 789, Surabaya', 'Asam urat');

INSERT INTO checkups (profile_id, tekanan_darah, gula_darah, catatan) VALUES 
(1, '140/90', 180, 'Tekanan darah tinggi, perlu kontrol rutin'),
(1, '130/85', 160, 'Membaik setelah minum obat'),
(2, '120/80', 110, 'Normal'),
(3, '135/88', 140, 'Sedikit tinggi, perlu diet');

-- Tampilkan struktur tabel
DESCRIBE profiles;
DESCRIBE checkups;

-- Tampilkan data contoh
SELECT * FROM profiles;
SELECT * FROM checkups;
